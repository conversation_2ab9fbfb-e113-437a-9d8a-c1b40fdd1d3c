package com.laive.api.channel;

import com.laive.core.config.RedisCacheConfig;
import com.laive.core.service.ChannelManager;
import com.laive.core.service.ChannelPackageManager;
import com.laive.core.vo.ChannelListVO;
import com.laive.core.domain.Channel;
import com.laive.core.domain.ChannelPackage;
import com.mysema.query.types.Order;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelService {

    private final ChannelManager channelManager;
    private final ChannelPackageManager channelPackageManager;

    @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS, key = "#listVO.platformId.concat('-').concat(#listVO.pageSize)" +
            ".concat('-').concat(#listVO.page).concat('-').concat(#listVO.type)")
    public ChannelListVO getCachedChannelListVO(ChannelListVO listVO) {
        return channelManager.getActivatedChannelListVO(listVO);
    }

    public ChannelListVO getChannelListVO(ChannelListVO listVO) {
        return channelManager.getActivatedChannelListVO(listVO);
    }

    /**
     * Cached method for getting channel packages by platform
     * Cache key: platformId-type (simple key for testing)
     */
    @Transactional(readOnly = true)
    @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS_BY_PACKAGE, key = "'packages-'.concat(#platformId).concat('-').concat(#type != null ? #type.toString() : 'null')")
    public ChannelPackageData getCachedChannelPackageData(String platformId, ChannelListVO.ChannelSearchType type) {
        log.info("[CACHE MISS] Loading channel package data from database for platformId: {}, type: {}", platformId, type);

        List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(platformId);
        List<Channel> orphanChannels = channelManager.getOrphanChannels(platformId, type);

        // Force initialization of lazy collections before caching
        for (ChannelPackage pkg : channelPackages) {
            // Force load ChannelPackage lazy collections
            if (pkg.getChannelPackagePrices() != null) {
                pkg.getChannelPackagePrices().size(); // Force lazy loading of channelPackagePrices
            }

            if (pkg.getChannels() != null) {
                pkg.getChannels().size(); // Force lazy loading of channels
                // Also force load nested lazy collections in channels
                for (Channel channel : pkg.getChannels()) {
                    if (channel.getIptvList() != null) {
                        channel.getIptvList().size(); // Force lazy loading of iptvList
                    }
                    if (channel.getChannelGenres() != null) {
                        channel.getChannelGenres().size(); // Force lazy loading of channelGenres
                    }
                    if (channel.getCountries() != null) {
                        channel.getCountries().size(); // Force lazy loading of countries
                    }
                }
            }
        }

        // Force initialization of lazy collections for orphan channels
        for (Channel channel : orphanChannels) {
            if (channel.getIptvList() != null) {
                channel.getIptvList().size(); // Force lazy loading of iptvList
            }
            if (channel.getChannelGenres() != null) {
                channel.getChannelGenres().size(); // Force lazy loading of channelGenres
            }
            if (channel.getCountries() != null) {
                channel.getCountries().size(); // Force lazy loading of countries
            }
        }

        log.info("[CACHE MISS] Loaded {} channel packages and {} orphan channels for platformId: {}",
                channelPackages.size(), orphanChannels.size(), platformId);

        return new ChannelPackageData(channelPackages, orphanChannels);
    }

    /**
     * Non-cached method for getting channel packages by platform
     */
    @Transactional(readOnly = true)
    public ChannelPackageData getChannelPackageData(String platformId, ChannelListVO.ChannelSearchType type) {
        log.info("[NO CACHE] Loading channel package data from database for platformId: {}, type: {}", platformId, type);

        List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(platformId);
        List<Channel> orphanChannels = channelManager.getOrphanChannels(platformId, type);

        // Force initialization of lazy collections
        for (ChannelPackage pkg : channelPackages) {
            // Force load ChannelPackage lazy collections
            if (pkg.getChannelPackagePrices() != null) {
                pkg.getChannelPackagePrices().size(); // Force lazy loading of channelPackagePrices
            }

            if (pkg.getChannels() != null) {
                pkg.getChannels().size(); // Force lazy loading of channels
                // Also force load nested lazy collections in channels
                for (Channel channel : pkg.getChannels()) {
                    if (channel.getIptvList() != null) {
                        channel.getIptvList().size(); // Force lazy loading of iptvList
                    }
                    if (channel.getChannelGenres() != null) {
                        channel.getChannelGenres().size(); // Force lazy loading of channelGenres
                    }
                    if (channel.getCountries() != null) {
                        channel.getCountries().size(); // Force lazy loading of countries
                    }
                }
            }
        }

        // Force initialization of lazy collections for orphan channels
        for (Channel channel : orphanChannels) {
            if (channel.getIptvList() != null) {
                channel.getIptvList().size(); // Force lazy loading of iptvList
            }
            if (channel.getChannelGenres() != null) {
                channel.getChannelGenres().size(); // Force lazy loading of channelGenres
            }
            if (channel.getCountries() != null) {
                channel.getCountries().size(); // Force lazy loading of countries
            }
        }

        log.info("[NO CACHE] Loaded {} channel packages and {} orphan channels for platformId: {}",
                channelPackages.size(), orphanChannels.size(), platformId);

        return new ChannelPackageData(channelPackages, orphanChannels);
    }

    // ========== NEW CACHE-THEN-FILTER METHODS ==========

    /**
     * Cache all channels by platform and type only (new cache-then-filter approach)
     * This method caches all channels without any filtering parameters
     */
    @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS_ALL,
            key = "'all-channels-' + #platformId + '-' + (#type != null ? #type : 'CATCHUP')")
    public List<Channel> getAllCachedChannels(String platformId, ChannelListVO.ChannelSearchType type) {
        log.info("[CACHE MISS - ALL CHANNELS] Loading all channels for platformId: {}, type: {}", platformId, type);

        ChannelListVO tempListVO = new ChannelListVO();
        tempListVO.setPlatformId(platformId);
        tempListVO.setType(type != null ? type : ChannelListVO.ChannelSearchType.CATCHUP);
        tempListVO.setEnabled(true);
        tempListVO.setPageSize(0); // No pagination - get all

        ChannelListVO result = channelManager.getActivatedChannelListVO(tempListVO);
        log.info("[CACHE MISS - ALL CHANNELS] Loaded {} channels for platformId: {}", result.getList().size(), platformId);

        return result.getList();
    }

    /**
     * Apply filters and pagination to cached data (new cache-then-filter approach)
     * This method gets all channels from cache and applies filters post-cache
     */
    public ChannelListVO getCachedChannelListVOWithFiltering(ChannelListVO listVO) {
        long startTime = System.currentTimeMillis();

        // Get all channels from cache
        List<Channel> allChannels = getAllCachedChannels(listVO.getPlatformId(), listVO.getType());
        long cacheTime = System.currentTimeMillis();

        // Apply all filters
        List<Channel> filteredChannels = applyAllFilters(allChannels, listVO);
        long filterTime = System.currentTimeMillis();

        // Apply pagination and return result
        ChannelListVO result = buildPaginatedResult(filteredChannels, listVO);
        long totalTime = System.currentTimeMillis();

        log.debug("[CACHE-THEN-FILTER] Performance - Cache: {}ms, Filter: {}ms, Total: {}ms, Results: {}/{}",
                cacheTime - startTime, filterTime - cacheTime, totalTime - startTime,
                result.getList().size(), allChannels.size());

        return result;
    }

    /**
     * Apply all filters to the channel list
     */
    private List<Channel> applyAllFilters(List<Channel> channels, ChannelListVO listVO) {

        Stream<Channel> stream = channels.stream();

        // Apply keyword filter
        if (listVO.getKeyword() != null && !listVO.getKeyword().trim().isEmpty()) {
            String keyword = listVO.getKeyword().toLowerCase().trim();
            stream = stream.filter(channel ->
                    channel.getName().toLowerCase().contains(keyword)
            );
        }

        // Apply country filter (geo-blocking)
        if (listVO.getCountryCode() != null) {
            stream = stream.filter(channel ->
                    !channel.isGeoBlocked() || channel.getCountries().stream()
                            .anyMatch(country -> country.getCca2().equalsIgnoreCase(listVO.getCountryCode()))
            );
        }

        // Apply sorting
        stream = applySorting(stream, listVO.getSortTargetColumn(), listVO.getAscendingOrder());

        return stream.collect(Collectors.toList());
    }

    /**
     * Apply sorting to the channel stream
     */
    private Stream<Channel> applySorting(Stream<Channel> stream, String sortTargetColumn, Order ascendingOrder) {
        if (sortTargetColumn == null || sortTargetColumn.trim().isEmpty()) {
            return stream.sorted(Comparator.comparing(Channel::getChannelNumber));
        }

        if (sortTargetColumn.equals("channelName")) {
            if (ascendingOrder.equals(Order.ASC)) {
                return stream.sorted(Comparator.comparing(Channel::getName));
            } else {
                return stream.sorted(Comparator.comparing(Channel::getName).reversed());
            }
        }

        return stream.sorted(
                Comparator.comparing(
                        Channel::getChannelNumber,
                        Comparator.nullsLast(Comparator.naturalOrder())
                )
        );

    }

    /**
     * Build paginated result from filtered channels
     */
    private ChannelListVO buildPaginatedResult(List<Channel> filteredChannels, ChannelListVO listVO) {
        //    enable, platform, type,
//    keyword,countrycode,pagination, targetedcolumn(channelName, channelNumber), order,

        ChannelListVO result = new ChannelListVO();
        result.setEnabled(true);
        result.setTimezone(listVO.getTimezone());
        result.setCountryCode(listVO.getCountryCode());
        result.setScheduleUpdateInterval(listVO.getScheduleUpdateInterval());
        result.setType(listVO.getType());
        result.setSortType(listVO.getSortType());

        result.setPlatformId(listVO.getPlatformId());
        result.setKeyword(listVO.getKeyword());
        result.setSortTargetColumn(listVO.getSortTargetColumn());
        result.setAscendingOrder(listVO.getAscendingOrder());

        // Set total count
        result.setFullListSize(filteredChannels.size());

        // Apply pagination
        if (listVO.getPageSize() > 0) {
            int startIndex = (listVO.getPage() - 1) * listVO.getPageSize();
            int endIndex = Math.min(startIndex + listVO.getPageSize(), filteredChannels.size());

            if (startIndex < filteredChannels.size() && startIndex >= 0) {
                result.setList(filteredChannels.subList(startIndex, endIndex));
            } else {
                result.setList(new ArrayList<>());
            }
        } else {
            // No pagination requested
            result.setList(filteredChannels);
        }

        result.setPage(listVO.getPage());
        result.setPageSize(listVO.getPageSize());

        return result;
    }

    // ========== END NEW METHODS ==========

    /**
     * Simple data holder for channel package data
     * Implements Serializable for Redis caching
     */
    public static class ChannelPackageData implements Serializable {
        private static final long serialVersionUID = 1L;

        private final List<ChannelPackage> channelPackages;
        private final List<Channel> orphanChannels;

        public ChannelPackageData(List<ChannelPackage> channelPackages, List<Channel> orphanChannels) {
            this.channelPackages = channelPackages;
            this.orphanChannels = orphanChannels;
        }

        public List<ChannelPackage> getChannelPackages() {
            return channelPackages;
        }

        public List<Channel> getOrphanChannels() {
            return orphanChannels;
        }
    }
}
