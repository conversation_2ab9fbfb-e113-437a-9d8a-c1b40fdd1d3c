package com.laive.api.channel;

import com.laive.core.config.RedisCacheConfig;
import com.laive.core.service.ChannelManager;
import com.laive.core.service.ChannelPackageManager;
import com.laive.core.vo.ChannelListVO;
import com.laive.core.domain.Channel;
import com.laive.core.domain.ChannelPackage;
import com.mysema.query.types.Order;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelService {

    private final ChannelManager channelManager;
    private final ChannelPackageManager channelPackageManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS, key = "#listVO.platformId.concat('-').concat(#listVO.pageSize)" +
            ".concat('-').concat(#listVO.page).concat('-').concat(#listVO.type)")
    public ChannelListVO getCachedChannelListVO(ChannelListVO listVO) {
        return channelManager.getActivatedChannelListVO(listVO);
    }

    public ChannelListVO getChannelListVO(ChannelListVO listVO) {
        return channelManager.getActivatedChannelListVO(listVO);
    }

    /**
     * Cached method for getting channel packages by platform
     * Cache key: platformId-type (simple key for testing)
     */
    @Transactional(readOnly = true)
    @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS_BY_PACKAGE, key = "'packages-'.concat(#platformId).concat('-').concat(#type != null ? #type.toString() : 'null')")
    public ChannelPackageData getCachedChannelPackageData(String platformId, ChannelListVO.ChannelSearchType type) {
        log.info("[CACHE MISS] Loading channel package data from database for platformId: {}, type: {}", platformId, type);

        List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(platformId);
        List<Channel> orphanChannels = channelManager.getOrphanChannels(platformId, type);

        // Force initialization of lazy collections before caching
        for (ChannelPackage pkg : channelPackages) {
            // Force load ChannelPackage lazy collections
            if (pkg.getChannelPackagePrices() != null) {
                pkg.getChannelPackagePrices().size(); // Force lazy loading of channelPackagePrices
            }

            if (pkg.getChannels() != null) {
                pkg.getChannels().size(); // Force lazy loading of channels
                // Also force load nested lazy collections in channels
                for (Channel channel : pkg.getChannels()) {
                    if (channel.getIptvList() != null) {
                        channel.getIptvList().size(); // Force lazy loading of iptvList
                    }
                    if (channel.getChannelGenres() != null) {
                        channel.getChannelGenres().size(); // Force lazy loading of channelGenres
                    }
                    if (channel.getCountries() != null) {
                        channel.getCountries().size(); // Force lazy loading of countries
                    }
                }
            }
        }

        // Force initialization of lazy collections for orphan channels
        for (Channel channel : orphanChannels) {
            if (channel.getIptvList() != null) {
                channel.getIptvList().size(); // Force lazy loading of iptvList
            }
            if (channel.getChannelGenres() != null) {
                channel.getChannelGenres().size(); // Force lazy loading of channelGenres
            }
            if (channel.getCountries() != null) {
                channel.getCountries().size(); // Force lazy loading of countries
            }
        }

        log.info("[CACHE MISS] Loaded {} channel packages and {} orphan channels for platformId: {}",
                channelPackages.size(), orphanChannels.size(), platformId);

        return new ChannelPackageData(channelPackages, orphanChannels);
    }

    /**
     * Non-cached method for getting channel packages by platform
     */
    @Transactional(readOnly = true)
    public ChannelPackageData getChannelPackageData(String platformId, ChannelListVO.ChannelSearchType type) {
        log.info("[NO CACHE] Loading channel package data from database for platformId: {}, type: {}", platformId, type);

        List<ChannelPackage> channelPackages = channelPackageManager.getAllListHavingChildChannel(platformId);
        List<Channel> orphanChannels = channelManager.getOrphanChannels(platformId, type);

        // Force initialization of lazy collections
        for (ChannelPackage pkg : channelPackages) {
            // Force load ChannelPackage lazy collections
            if (pkg.getChannelPackagePrices() != null) {
                pkg.getChannelPackagePrices().size(); // Force lazy loading of channelPackagePrices
            }

            if (pkg.getChannels() != null) {
                pkg.getChannels().size(); // Force lazy loading of channels
                // Also force load nested lazy collections in channels
                for (Channel channel : pkg.getChannels()) {
                    if (channel.getIptvList() != null) {
                        channel.getIptvList().size(); // Force lazy loading of iptvList
                    }
                    if (channel.getChannelGenres() != null) {
                        channel.getChannelGenres().size(); // Force lazy loading of channelGenres
                    }
                    if (channel.getCountries() != null) {
                        channel.getCountries().size(); // Force lazy loading of countries
                    }
                }
            }
        }

        // Force initialization of lazy collections for orphan channels
        for (Channel channel : orphanChannels) {
            if (channel.getIptvList() != null) {
                channel.getIptvList().size(); // Force lazy loading of iptvList
            }
            if (channel.getChannelGenres() != null) {
                channel.getChannelGenres().size(); // Force lazy loading of channelGenres
            }
            if (channel.getCountries() != null) {
                channel.getCountries().size(); // Force lazy loading of countries
            }
        }

        log.info("[NO CACHE] Loaded {} channel packages and {} orphan channels for platformId: {}",
                channelPackages.size(), orphanChannels.size(), platformId);

        return new ChannelPackageData(channelPackages, orphanChannels);
    }

    // ========== NEW CACHE-THEN-FILTER METHODS ==========

    /**
     * Cache all channels by platform and type only (new cache-then-filter approach)
     * This method caches all channels without any filtering parameters
     */
    @Transactional(readOnly = true)
    @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS_ALL,
            key = "'all-channels-'.concat(#platformId).concat('-').concat(#type != null ? #type.toString() : 'CATCHUP')")
    public List<Channel> getAllCachedChannels(String platformId, ChannelListVO.ChannelSearchType type) {
        String cacheKey = "all-channels-" + platformId + "-" + (type != null ? type : ChannelListVO.ChannelSearchType.CATCHUP);
        log.error("[CACHE MISS - ALL CHANNELS] Cache key: {}, Loading all channels for platformId: {}, type: {}", cacheKey, platformId, type);
        log.error("[CACHE MISS - ALL CHANNELS] Cache region: {}, Thread: {}", RedisCacheConfig.CACHE_CHANNELS_ALL, Thread.currentThread().getName());

        ChannelListVO tempListVO = new ChannelListVO();
        tempListVO.setPlatformId(platformId);
        tempListVO.setType(type != null ? type : ChannelListVO.ChannelSearchType.CATCHUP);
        tempListVO.setEnabled(true);
        tempListVO.setPageSize(0); // No pagination - get all

        ChannelListVO result = channelManager.getActivatedChannelListVO(tempListVO);

        // Force initialization of lazy collections before caching (same as working method)
        for (Channel channel : result.getList()) {
            if (channel.getIptvList() != null) {
                channel.getIptvList().size(); // Force lazy loading of iptvList
            }
            if (channel.getChannelGenres() != null) {
                channel.getChannelGenres().size(); // Force lazy loading of channelGenres
            }
            if (channel.getCountries() != null) {
                channel.getCountries().size(); // Force lazy loading of countries
            }
        }

        log.warn("[CACHE MISS - ALL CHANNELS] Loaded {} channels for platformId: {}, cache key: {}", result.getList().size(), platformId, cacheKey);

        return result.getList();
    }

    /**
     * Apply filters and pagination to cached data (new cache-then-filter approach)
     * This method gets all channels from cache and applies filters post-cache
     */
    public ChannelListVO getCachedChannelListVOWithFiltering(ChannelListVO listVO) {
        long startTime = System.currentTimeMillis();
        String cacheKey = "all-channels-" + listVO.getPlatformId() + "-" + (listVO.getType() != null ? listVO.getType() : ChannelListVO.ChannelSearchType.CATCHUP);
        log.warn("[CACHE-THEN-FILTER] Attempting to get channels with cache key: {}", cacheKey);

        // Get all channels from cache
        List<Channel> allChannels = getAllCachedChannels(listVO.getPlatformId(), listVO.getType());
        long cacheTime = System.currentTimeMillis();

        // Determine if it was a cache hit or miss based on timing
        long cacheOperationTime = cacheTime - startTime;
        String cacheStatus = cacheOperationTime < 10 ? "CACHE HIT" : "CACHE MISS";
        log.error("[{}] Retrieved {} channels (cache operation took {}ms)", cacheStatus, allChannels.size(), cacheOperationTime);

        // Apply all filters
        List<Channel> filteredChannels = applyAllFilters(allChannels, listVO);
        long filterTime = System.currentTimeMillis();

        // Apply pagination and return result
        ChannelListVO result = buildPaginatedResult(filteredChannels, listVO);
        long totalTime = System.currentTimeMillis();

        log.warn("[CACHE-THEN-FILTER] Performance - Cache: {}ms, Filter: {}ms, Total: {}ms, Results: {}/{}",
                cacheTime - startTime, filterTime - cacheTime, totalTime - startTime,
                result.getList().size(), allChannels.size());

        return result;
    }

    /**
     * Apply all filters to the channel list
     */
    private List<Channel> applyAllFilters(List<Channel> channels, ChannelListVO listVO) {

        Stream<Channel> stream = channels.stream();

        // Apply keyword filter
        if (listVO.getKeyword() != null && !listVO.getKeyword().trim().isEmpty()) {
            String keyword = listVO.getKeyword().toLowerCase().trim();
            stream = stream.filter(channel ->
                    channel.getName().toLowerCase().contains(keyword)
            );
        }

        // Apply country filter (geo-blocking)
        if (listVO.getCountryCode() != null) {
            stream = stream.filter(channel ->
                    !channel.isGeoBlocked() || channel.getCountries().stream()
                            .anyMatch(country -> country.getCca2().equalsIgnoreCase(listVO.getCountryCode()))
            );
        }

        // Apply sorting
        stream = applySorting(stream, listVO.getSortTargetColumn(), listVO.getAscendingOrder());

        return stream.collect(Collectors.toList());
    }

    /**
     * Apply sorting to the channel stream
     */
    private Stream<Channel> applySorting(Stream<Channel> stream, String sortTargetColumn, Order ascendingOrder) {
        if (sortTargetColumn == null || sortTargetColumn.trim().isEmpty()) {
            return stream.sorted(Comparator.comparing(Channel::getChannelNumber));
        }

        if (sortTargetColumn.equals("channelName")) {
            if (ascendingOrder.equals(Order.ASC)) {
                return stream.sorted(Comparator.comparing(Channel::getName));
            } else {
                return stream.sorted(Comparator.comparing(Channel::getName).reversed());
            }
        }

        return stream.sorted(
                Comparator.comparing(
                        Channel::getChannelNumber,
                        Comparator.nullsLast(Comparator.naturalOrder())
                )
        );

    }

    /**
     * Build paginated result from filtered channels
     */
    private ChannelListVO buildPaginatedResult(List<Channel> filteredChannels, ChannelListVO listVO) {
        //    enable, platform, type,
//    keyword,countrycode,pagination, targetedcolumn(channelName, channelNumber), order,

        ChannelListVO result = new ChannelListVO();
        result.setEnabled(true);
        result.setTimezone(listVO.getTimezone());
        result.setCountryCode(listVO.getCountryCode());
        result.setScheduleUpdateInterval(listVO.getScheduleUpdateInterval());
        result.setType(listVO.getType());
        result.setSortType(listVO.getSortType());

        result.setPlatformId(listVO.getPlatformId());
        result.setKeyword(listVO.getKeyword());
        result.setSortTargetColumn(listVO.getSortTargetColumn());
        result.setAscendingOrder(listVO.getAscendingOrder());

        // Set total count
        result.setFullListSize(filteredChannels.size());

        // Apply pagination
        if (listVO.getPageSize() > 0) {
            int startIndex = (listVO.getPage() - 1) * listVO.getPageSize();
            int endIndex = Math.min(startIndex + listVO.getPageSize(), filteredChannels.size());

            if (startIndex < filteredChannels.size() && startIndex >= 0) {
                result.setList(filteredChannels.subList(startIndex, endIndex));
            } else {
                result.setList(new ArrayList<>());
            }
        } else {
            // No pagination requested
            result.setList(filteredChannels);
        }

        result.setPage(listVO.getPage());
        result.setPageSize(listVO.getPageSize());

        return result;
    }

    /**
   * Test method to check if cache is working
   * Call this method twice with same parameters - first should be cache miss, second should be cache hit
   */
  public String testCacheStatus(String platformId, ChannelListVO.ChannelSearchType type) {
    long startTime = System.currentTimeMillis();
    List<Channel> channels = getAllCachedChannels(platformId, type);
    long endTime = System.currentTimeMillis();

    String result = String.format("Cache test - PlatformId: %s, Type: %s, Channels: %d, Time: %dms",
                                  platformId, type, channels.size(), endTime - startTime);
    log.warn("[CACHE TEST] {}", result);
    return result;
  }

  /**
   * Test Redis connection and cache operations directly
   */
  public String testRedisConnection() {
    try {
      String testKey = "test-cache-key-" + System.currentTimeMillis();
      String testValue = "test-value-" + System.currentTimeMillis();

      // Test Redis write
      redisTemplate.opsForValue().set(testKey, testValue);
      log.error("[REDIS TEST] Successfully wrote to Redis: {} = {}", testKey, testValue);

      // Test Redis read
      Object retrievedValue = redisTemplate.opsForValue().get(testKey);
      log.error("[REDIS TEST] Successfully read from Redis: {} = {}", testKey, retrievedValue);

      // Test Redis delete
      redisTemplate.delete(testKey);
      log.error("[REDIS TEST] Successfully deleted from Redis: {}", testKey);

      return "Redis connection test PASSED";
    } catch (Exception e) {
      log.error("[REDIS TEST] Redis connection test FAILED", e);
      return "Redis connection test FAILED: " + e.getMessage();
    }
  }

  /**
   * Check if cache key exists in Redis - try multiple key formats
   */
  public String checkCacheKeyExists(String platformId, ChannelListVO.ChannelSearchType type) {
    try {
      String baseKey = "all-channels-" + platformId + "-" + (type != null ? type : ChannelListVO.ChannelSearchType.CATCHUP);

      // Try different possible key formats
      String[] possibleKeys = {
        "CACHE_CHANNELS_ALL::" + baseKey,
        "CACHE_CHANNELS_ALL:" + baseKey,
        baseKey,
        "channels:" + baseKey,
        "cache:" + baseKey
      };

      StringBuilder result = new StringBuilder();
      result.append("Checking cache keys for base: ").append(baseKey).append("\n");

      boolean found = false;
      for (String key : possibleKeys) {
        Boolean exists = redisTemplate.hasKey(key);
        result.append("Key: '").append(key).append("' exists: ").append(exists).append("\n");
        log.error("[CACHE KEY CHECK] Key: {}, Exists: {}", key, exists);

        if (exists) {
          found = true;
          Object value = redisTemplate.opsForValue().get(key);
          result.append("Value type: ").append(value != null ? value.getClass().getSimpleName() : "null").append("\n");
          log.error("[CACHE KEY CHECK] Found value type: {}", value != null ? value.getClass().getSimpleName() : "null");
        }
      }

      // Also check all keys with pattern
      try {
        java.util.Set<String> allKeys = redisTemplate.keys("*" + baseKey + "*");
        result.append("\nAll keys containing '").append(baseKey).append("': ").append(allKeys).append("\n");
        log.error("[CACHE KEY CHECK] All matching keys: {}", allKeys);
      } catch (Exception e) {
        result.append("\nError getting all keys: ").append(e.getMessage()).append("\n");
      }

      if (!found) {
        result.append("\nNO CACHE KEYS FOUND - Cache is not working!");
      }

      return result.toString();
    } catch (Exception e) {
      log.error("[CACHE KEY CHECK] Error checking cache key", e);
      return "Error checking cache key: " + e.getMessage();
    }
  }

  /**
   * Check all Redis keys to see what's actually being stored
   */
  public String getAllRedisKeys() {
    try {
      java.util.Set<String> allKeys = redisTemplate.keys("*");
      StringBuilder result = new StringBuilder();
      result.append("Total Redis keys: ").append(allKeys.size()).append("\n");

      for (String key : allKeys) {
        result.append("Key: ").append(key).append("\n");
        log.error("[ALL REDIS KEYS] Key: {}", key);
      }

      return result.toString();
    } catch (Exception e) {
      log.error("[ALL REDIS KEYS] Error getting all keys", e);
      return "Error getting all keys: " + e.getMessage();
    }
  }

  /**
   * Simple cache test with String return type
   */
  @Cacheable(value = "simple-test", key = "#input")
  public String simpleCacheTest(String input) {
    log.error("[SIMPLE CACHE MISS] Input: {}", input);
    return "Cached result for: " + input + " at " + System.currentTimeMillis();
  }

  /**
   * Test using the same cache region as the working method
   */
  @Transactional(readOnly = true)
  @Cacheable(value = RedisCacheConfig.CACHE_CHANNELS_BY_PACKAGE,
             key = "'test-channels-'.concat(#platformId).concat('-').concat(#type != null ? #type.toString() : 'CATCHUP')")
  public String testWithWorkingCacheRegion(String platformId, ChannelListVO.ChannelSearchType type) {
    log.error("[TEST WORKING CACHE REGION MISS] PlatformId: {}, Type: {}", platformId, type);
    return "Test result for: " + platformId + "-" + type + " at " + System.currentTimeMillis();
  }

  /**
   * Debug method to check cache region values
   */
  public String debugCacheRegions() {
    StringBuilder result = new StringBuilder();
    result.append("Cache Region Values:\n");
    result.append("CACHE_CHANNELS_ALL = '").append(RedisCacheConfig.CACHE_CHANNELS_ALL).append("'\n");
    result.append("CACHE_CHANNELS_BY_PACKAGE = '").append(RedisCacheConfig.CACHE_CHANNELS_BY_PACKAGE).append("'\n");
    result.append("CACHE_CHANNELS = '").append(RedisCacheConfig.CACHE_CHANNELS).append("'\n");

    log.error("[DEBUG CACHE REGIONS] {}", result.toString());
    return result.toString();
  }

  // ========== END NEW METHODS ==========

    /**
     * Simple data holder for channel package data
     * Implements Serializable for Redis caching
     */
    public static class ChannelPackageData implements Serializable {
        private static final long serialVersionUID = 1L;

        private final List<ChannelPackage> channelPackages;
        private final List<Channel> orphanChannels;

        public ChannelPackageData(List<ChannelPackage> channelPackages, List<Channel> orphanChannels) {
            this.channelPackages = channelPackages;
            this.orphanChannels = orphanChannels;
        }

        public List<ChannelPackage> getChannelPackages() {
            return channelPackages;
        }

        public List<Channel> getOrphanChannels() {
            return orphanChannels;
        }
    }
}
