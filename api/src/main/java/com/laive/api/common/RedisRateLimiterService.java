package com.laive.api.common;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class RedisRateLimiterService {

    private final RedisTemplate<String, Object> redisTemplate;

    // Configuration
    private static final int MAX_REQUESTS = 10;
    private static final int WINDOW_SIZE_SECONDS = 10;
    private static final String KEY_PREFIX = "rate_limit:getListByPackage:";

    /**
     * Check if the IP address is allowed to make a request using Redis
     * @param ipAddress The client IP address
     * @return true if allowed, false if rate limit exceeded
     */
    public boolean isAllowed(String ipAddress) {
        String redisKey = KEY_PREFIX + ipAddress;

        log.warn("[REDIS RATE LIMIT] Checking rate limit for IP: {}, key: {}", ipAddress, redisKey);

        try {
            // Use Redis INCR for atomic increment (returns new value)
            Long currentCount = redisTemplate.opsForValue().increment(redisKey, 1);

            if (currentCount == 1) {
                // First request for this IP - set TTL
                redisTemplate.expire(redisKey, WINDOW_SIZE_SECONDS, TimeUnit.SECONDS);
                log.warn("[REDIS RATE LIMIT] First request for IP: {}. Count: 1/{}", ipAddress, MAX_REQUESTS);
                return true;
            } else if (currentCount <= MAX_REQUESTS) {
                // Request allowed
                log.warn("[REDIS RATE LIMIT] Request allowed for IP: {}. Count: {}/{}",
                         ipAddress, currentCount, MAX_REQUESTS);
                return true;
            } else {
                // Rate limit exceeded
                log.warn("[REDIS RATE LIMIT] Request BLOCKED for IP: {}. Limit exceeded: {}/{}",
                         ipAddress, currentCount, MAX_REQUESTS);
                return false;
            }

        } catch (Exception e) {
            log.error("[REDIS RATE LIMIT] Redis error for IP: {}. Allowing request as fallback. Error: {}",
                     ipAddress, e.getMessage());
            // Fallback: allow request if Redis is down
            return true;
        }
    }

    /**
     * Get current request count for an IP (for debugging)
     */
    public int getCurrentCount(String ipAddress) {
        String redisKey = KEY_PREFIX + ipAddress;

        try {
            Object count = redisTemplate.opsForValue().get(redisKey);
            if (count == null) {
                return 0;
            }
            // Handle both Integer and Long types
            if (count instanceof Number) {
                return ((Number) count).intValue();
            }
            return Integer.parseInt(count.toString());
        } catch (Exception e) {
            log.error("[REDIS RATE LIMIT] Error getting count for IP: {}. Clearing corrupted key. Error: {}", ipAddress, e.getMessage());
            // Clear the corrupted key and return 0
            try {
                redisTemplate.delete(redisKey);
                log.warn("[REDIS RATE LIMIT] Cleared corrupted key for IP: {}", ipAddress);
            } catch (Exception deleteError) {
                log.error("[REDIS RATE LIMIT] Failed to clear corrupted key for IP: {}. Error: {}", ipAddress, deleteError.getMessage());
            }
            return 0;
        }
    }

    /**
     * Get remaining TTL for an IP's rate limit (for debugging)
     */
    public long getRemainingTTL(String ipAddress) {
        String redisKey = KEY_PREFIX + ipAddress;

        try {
            return redisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[REDIS RATE LIMIT] Error getting TTL for IP: {}. Clearing corrupted key. Error: {}", ipAddress, e.getMessage());
            // Clear the corrupted key
            try {
                redisTemplate.delete(redisKey);
                log.warn("[REDIS RATE LIMIT] Cleared corrupted key for IP: {} during TTL check", ipAddress);
            } catch (Exception deleteError) {
                log.error("[REDIS RATE LIMIT] Failed to clear corrupted key for IP: {} during TTL check. Error: {}", ipAddress, deleteError.getMessage());
            }
            return -1;
        }
    }

    /**
     * Manually reset rate limit for an IP (for testing)
     */
    public void resetRateLimit(String ipAddress) {
        String redisKey = KEY_PREFIX + ipAddress;

        try {
            redisTemplate.delete(redisKey);
            log.warn("[REDIS RATE LIMIT] Rate limit reset for IP: {}", ipAddress);
        } catch (Exception e) {
            log.error("[REDIS RATE LIMIT] Error resetting rate limit for IP: {}. Error: {}", ipAddress, e.getMessage());
        }
    }

    /**
     * Get debug information about rate limiting
     */
    public void printDebugInfo(String ipAddress) {
        int count = getCurrentCount(ipAddress);
        long ttl = getRemainingTTL(ipAddress);

        log.warn("[REDIS RATE LIMIT] Debug - IP: {}, Count: {}/{}, TTL: {} seconds",
                 ipAddress, count, MAX_REQUESTS, ttl);
    }

    /**
     * Clear all rate limit data (for testing/cleanup)
     */
    public void clearAllRateLimits() {
        try {
            Set<String> keys = redisTemplate.keys(KEY_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.warn("[REDIS RATE LIMIT] Cleared {} rate limit entries", keys.size());
            } else {
                log.info("[REDIS RATE LIMIT] No rate limit keys found to clear");
            }
        } catch (Exception e) {
            log.error("[REDIS RATE LIMIT] Error clearing rate limits. Error: {}", e.getMessage());
        }
    }

    /**
     * Clear corrupted rate limit keys (for fixing deserialization issues)
     */
    public void clearCorruptedKeys() {
        try {
            Set<String> keys = redisTemplate.keys(KEY_PREFIX + "*");
            if (keys == null || keys.isEmpty()) {
                log.info("[REDIS RATE LIMIT] No rate limit keys found");
                return;
            }

            int clearedCount = 0;
            for (String key : keys) {
                try {
                    // Try to read the value to check if it's corrupted
                    Object value = redisTemplate.opsForValue().get(key);
                    if (value != null && !(value instanceof Number)) {
                        // If it's not a number, it might be corrupted
                        redisTemplate.delete(key);
                        clearedCount++;
                        log.warn("[REDIS RATE LIMIT] Cleared corrupted key: {}", key);
                    }
                } catch (Exception e) {
                    // If we can't read it, it's definitely corrupted
                    try {
                        redisTemplate.delete(key);
                        clearedCount++;
                        log.warn("[REDIS RATE LIMIT] Cleared corrupted key: {} (read error: {})", key, e.getMessage());
                    } catch (Exception deleteError) {
                        log.error("[REDIS RATE LIMIT] Failed to delete corrupted key: {}. Error: {}", key, deleteError.getMessage());
                    }
                }
            }

            log.info("[REDIS RATE LIMIT] Corruption cleanup completed. Cleared {} corrupted keys out of {} total keys", clearedCount, keys.size());
        } catch (Exception e) {
            log.error("[REDIS RATE LIMIT] Error during corruption cleanup. Error: {}", e.getMessage());
        }
    }
}
