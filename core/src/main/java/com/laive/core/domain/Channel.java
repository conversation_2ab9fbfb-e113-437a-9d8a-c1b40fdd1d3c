package com.laive.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laive.core.base.BaseTimestampEntity;
import com.laive.core.constants.ColumnSizeConstants;
import com.laive.core.util.BooleanUtils;
import com.laive.core.util.CollectionUtils;
import com.laive.core.util.ImageUrlUtil;
import com.laive.core.util.StringUtils;
import com.laive.core.vo.ScheduleByPassVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.Lob;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.*;
import java.util.stream.Collectors;

@Entity
@Table(name = "mt_channel")
@Data
@EqualsAndHashCode(callSuper = false, of = { "id" })
@NoArgsConstructor
@JsonIgnoreProperties(value = { "channelPackage", "enabled", "monitors", "platform", "instance", "transcoderInput", "image", "snap", "description",
    "updatedDt", "createdDt", "channelPackage", "channelPackages" })
@ToString(exclude = {"iptvList"})
public class Channel extends BaseTimestampEntity<String> implements Comparable<Channel> {

  private static final long serialVersionUID = -694621316705909359L;

  @Id
  @Column(length = ColumnSizeConstants.UUID)
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  @GeneratedValue(generator = "system-uuid")
  private String id;

  @ManyToOne(optional = false)
  @JoinColumn(name = "platform_id")
  private Platform platform;

  // TODO 다음업데이트에 제거.
  @Deprecated
  @ManyToOne
  @JoinColumn(name = "channel_package_id")
  private ChannelPackage channelPackage;

  @ManyToMany(cascade = { CascadeType.MERGE, CascadeType.PERSIST }, fetch = FetchType.EAGER)
  @JoinTable(name = "mt_channel_channel_package", joinColumns = @JoinColumn(name = "channel_id"), inverseJoinColumns = @JoinColumn(name = "channel_package_id"))
  @JsonProperty
  private Set<ChannelPackage> channelPackages = new HashSet<>();


  @ManyToOne
  @JoinTable(name = "nt_instance_channel", joinColumns = { @JoinColumn(name = "channel_id") }, inverseJoinColumns = { @JoinColumn(name = "instance_id") })
  private Instance instance;

  @ManyToOne(cascade = CascadeType.ALL)
  @JoinTable(name = "nt_transcoder_input_channel", joinColumns = { @JoinColumn(name = "channel_id") }, inverseJoinColumns = { @JoinColumn(name = "transcoder_input_id") })
  private TranscoderInput transcoderInput;

  @Column(name = "name", length = ColumnSizeConstants.NAME)
  private String name;

  @Column(name = "image", length = ColumnSizeConstants.FILE_NAME)
  private String image;

  @JsonProperty("number")
  @Column(name = "channel_number")
  private Integer channelNumber;

  @Column(name = "snap", length = ColumnSizeConstants.FILE_NAME)
  private String snap;

  @Column(name = "ott_image", length = ColumnSizeConstants.FILE_NAME)
  private String ottImage;

  @Enumerated(EnumType.STRING)
  @Column(length = ColumnSizeConstants.TYPE)
  private ChannelType type;

  @Column(length = ColumnSizeConstants.IP)
  @JsonIgnore
  private String ip;

  @Lob
  private String description;

  @Column
  @JsonIgnore
  private boolean enabled;

  @Column(name = "inventory_id_m", length = 100)
  @JsonIgnore
  private String mobileInventoryId; // gittd 요청.

  @Column(name = "inventory_id_t", length = 100)
  @JsonIgnore
  private String tvInventoryId; // gittd 요청.

  @ManyToMany(mappedBy = "channels", fetch = FetchType.LAZY, cascade = { CascadeType.MERGE, CascadeType.PERSIST })
  private List<Monitor> monitors = new ArrayList<>();

  @Column
  private boolean free;

  @OneToMany(mappedBy = "channel", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
  private Set<ChannelPrice> channelPrices = new HashSet<>();

  @ManyToMany(cascade = { CascadeType.MERGE, CascadeType.PERSIST }, fetch = FetchType.EAGER)
  @JoinTable(name = "mt_channel_channel_genre", joinColumns = @JoinColumn(name = "channel_id"), inverseJoinColumns = @JoinColumn(name = "channel_genre_id"))
  @JsonProperty
  private Set<ChannelGenre> channelGenres = new HashSet<>();

  @Column
  @ColumnDefault("0")
  private boolean catchup;

  @Column(name = "poc")
  @ColumnDefault("0")
  private boolean poc;

  @Column(name = "poc_from")
  private Date pocFrom;

  @Column(name = "geo_blocked")
  @ColumnDefault("0")
  private boolean geoBlocked;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  @JoinTable(name = "mt_allowed_geo_channel", joinColumns = @JoinColumn(name = "channel_id"), inverseJoinColumns = @JoinColumn(name = "geo_country_id"))
  @JsonIgnore
  private Set<GeoCountry> countries = new HashSet<>();

  @OneToMany(mappedBy = "channel", orphanRemoval = true)
  @JsonManagedReference
  private List<IpTv> iptvList = new ArrayList<>();

  @Transient
  @JsonIgnore
  private List<IpTv> iptvListTemp = new ArrayList<>();


  @Deprecated
  private Double rating;

  @Transient
  @JsonIgnore
  private List<ChannelPrice> channelPricesTemp;

  @Transient
  @JsonProperty
  private ScheduleByPassVO onAirSchedule;

  @Transient
  private String[] channelScheduleMapperChannelId;

  @Transient
  private Integer[] genreIds;

  @Transient
  private String[] countryIds;

  @Transient
  private String[] channelPackageIdsTemp;

  public Channel(String channelId) {
    this.id = channelId;
  }

  @JsonProperty
  public String getSnapImageUrl() {
    return snap;
  }

  @JsonProperty
  public String getImageUrl() {
    if (StringUtils.isBlank(this.image))
      return "";
    return String.format("%s/%s/%s", ImageUrlUtil.getBaseImageUrl(this.getClass()), this.id, this.image);
  }

  @JsonProperty("free")
  public Integer isFreeChannel() {
    return BooleanUtils.toInteger(this.free);
  }

  @JsonProperty("ip")
  public String getIpString() {
    String targetWord = "/linear/channel";
    if (this.ip == null || !StringUtils.contains(this.ip, targetWord))
      return this.ip;
    return StringUtils.replace(this.ip, targetWord, String.format("/%s%s", this.platform.getId(), targetWord));
  }

  @JsonProperty
  public String getOttImageUrl() {
    if (StringUtils.isBlank(this.ottImage))
      return "";
    return String.format("%s/%s/%s", ImageUrlUtil.getBaseImageUrl(this.getClass()), this.id, this.ottImage);
  }

  @Transient
  public void addGenres(ChannelGenre channelGenre) {
    this.getChannelGenres().add(channelGenre);
  }

  @Transient
  public void addCountries(GeoCountry country) {
    this.getCountries().add(country);
  }

  @Transient
  public String getChannelPackageNames() {
    if (CollectionUtils.isNotEmpty(this.channelPackages)) {
      return this.channelPackages.stream().map(ChannelPackage::getName).collect(Collectors.joining("<br>"));
    }
    return "";
  }

  @Transient
  public String[] getChannelPackageIds() {
    if (CollectionUtils.isNotEmpty(this.channelPackages)) {
      return this.channelPackages.stream().map(ChannelPackage::getId).collect(Collectors.toList()).toArray(new String[this.channelPackages.size()]);
    }
    return null;
  }

  @Override
  public int compareTo(Channel other) {
    // Handle null objects
    if (other == null) {
      return -1;
    }

    // Primary comparison: channelNumber
    if (this.channelNumber == null && other.channelNumber == null) {
      // If both channelNumbers are null, compare by name
      if (this.name != null && other.name != null) {
        return this.name.compareTo(other.name);
      }
      // If names are also null, compare by ID
      if (this.id != null && other.id != null) {
        return this.id.compareTo(other.id);
      }
      return 0;
    }

    if (this.channelNumber == null) {
      return 1; // null channelNumbers go to end
    }

    if (other.channelNumber == null) {
      return -1; // null channelNumbers go to end
    }

    // Both channelNumbers are non-null
    int channelNumberComparison = this.channelNumber.compareTo(other.channelNumber);
    if (channelNumberComparison != 0) {
      return channelNumberComparison;
    }

    // If channelNumbers are equal, compare by name as secondary sort
    if (this.name != null && other.name != null) {
      return this.name.compareTo(other.name);
    }

    // Fallback to ID comparison
    if (this.id != null && other.id != null) {
      return this.id.compareTo(other.id);
    }

    return 0;
  }
}
