package com.laive.core.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@Slf4j
public class ServicePropertiesUtil {

  /****************
   * service properties
   *************/
  @Getter
  @Value("${storage.path}")
  private String storagePath;

  @Getter
  @Value("${subtitle.upload.path}")
  private String subtitleUploadPath;

  @Getter
  @Value("${storage.host}")
  private String storageHost;

  @Getter
  @Value("${location.snap.vod}")
  private String vodSnapDir;

  @Getter
  @Value("${location.snap.catchup}")
  private String catchupSnapDir;

  @Getter
  @Value("${upload.uri}")
  private String uploadUri;

  @Getter
  @Value("${geoip.uri}")
  private String geoipUri;

  @Getter
  private String geoipPath;

  @Getter
  @Value("${geoip.storage.path}")
  private String geoipStoragePath;

  @Getter
  @Value("${service.defaultLanguage}")
  private String defaultLanguage;

  @Getter
  @Value("${service.timezone}")
  private String serviceTimezone;

  @Getter
  @Value("${gmmanager.address}")
  private String gmmanagerAddress;

  @Getter
  @Value("${gmmanager.getEncoderUri}")
  private String gmmanagerGetEncoderUri;

  @Getter
  @Value("${gmmanager.cancelEncodeUri}")
  private String gmmanagerCancelEncodeUri;

  @Getter
  @Value("${gmmanager.channelEnableUri}")
  private String gmmanagerChannelEnableUri;

  @Getter
  @Value("${gmmanager.channelDisableUri}")
  private String gmmanagerChannelDisableUri;

  @Getter
  @Value("${gmmanager.channelDeleteUri}")
  private String gmmanagerChannelDeleteUri;

  @Value("${signedUri.library}")
  private String signedUriLibrary;

  @Value("${drmUri.channelVod}")
  private String drmUriChannelVod;

  @Value("${signedUri.channel}")
  private String signedUrlChannel;

  @Value("${drmUri.library}")
  private String drmUriLibrary;

  @Value("${drmUri.auth}")
  private String drmUriAuth;

  @Getter
  @Value("${mail.personal}")
  private String mailPersonal;

  @Getter
  @Value("${mail.username}")
  private String mailUsername;

  @Getter
  @Value("${password.regex}")
  private String passwordRegex;

  @Getter
  @Value("${project.build.timestamp}")
  private String projectBuildtimestamp;

  @Getter
  private String gmmanagerGetEncoderUrl;

  @Getter
  private String gmmanagerSetTodayScheduleUrl;

  @Getter
  private String gmmanagerStartNowUrl;

  @Getter
  private String gmmanagerCancelEncodeUrl;

  @Getter
  private String gmmanagerChannelEnableUrl;

  @Getter
  private String gmmanagerChannelDisableUrl;

  @Getter
  private String gmmanagerChannelDeleteUrl;

  @Getter
  @Value("${server.resourceServerUrl}")
  private String resourceServerUrl;

  @Getter
  private String authDrmUrl;

  @Getter
  private String uploadUrl;

  @Getter
  private String libraryUrl;

  @Getter
  private String channelVodUrl;

  @Getter
  private String channelVodDrmUrl;

  @Getter
  private String libraryDrmUrl;

  @Getter
  @Value("${currency.unit}")
  private String currencyUnit;

  @Getter
  @Value("${gmmanager.getInstanceUsageUri}")
  private String gmmanagerGetInstanceUsageUri;

  @Getter
  private String gmmanagerGetInstanceUsageUrl;

  @Getter
  @Value("${gmmanager.aws.startUri}")
  private String gmmanagerAwsStartUri;

  @Getter
  @Value("${gmmanager.aws.stopUri}")
  private String gmmanagerAwsStopUri;

  @Getter
  @Value("${gmmanager.aws.terminateUri}")
  private String gmmanagerAwsTerminateUri;

  @Getter
  @Value("${gmmanager.aws.createUri}")
  private String gmmanagerAwsCreateUri;

  @Getter
  @Value("${gmmanager.aws.checkStatusUri}")
  private String gmmanagerAwsCheckStatusUri;

  @Getter
  @Value("${gmmanager.setTodayScheduleUri}")
  private String gmmanagerSetTodayScheduleUri;

  @Getter
  @Value("${gmmanager.startNowUri}")
  private String gmmanagerStartNowUri;

  @Getter
  @Value("${gmmanager.distributeImageToLocalCdnUri}")
  private String gmmanagerDistributeImageToLocalCdnUri;

  @Getter
  @Value("${gmmanager.fairPlayPackagingUri}")
  private String gmmanagerFairPlayPackagingRequestUri;

  @Getter
  @Value("${gmmanager.soundTrackLanguageChangeUri}")
  private String gmmanagerSoundTrackLanguageChangeRequestUri;

  @Getter
  @Value("${gmmanager.thumbnailUri}")
  private String gmmanagerThumbnailUri;

  @Getter
  @Value("${gmmanager.scheduleThumbnailUri}")
  private String gmmanagerScheduleThumbnailUri;

  @Getter
  private String gmmanagerAwsStartUrl;

  @Getter
  private String gmmanagerAwsStopUrl;

  @Getter
  private String gmmanagerAwsTerminateUrl;

  @Getter
  private String gmmanagerAwsCreateUrl;

  @Getter
  private String gmmanagerAwsCheckStatusUrl;

  @Getter
  private String gmmanagerDistributeImageToLocalCdnUrl;

  @Getter
  private String channelLiveUrl;

  @Getter
  private String gmmanagerSoundTrackLanguageChangeRequestUrl;

  @Getter
  private String gmmanagerFairPlayPackagingRequestUrl;

  @Getter
  private String gmmanagerThumbnailUrl;

  @Getter
  private String gmmanagerScheduleThumbnailUrl;

  @Getter
  @Value("${imageMagick.path}")
  private String imageMagickPath;

  @Getter
  @Value("${licenseFile.path}")
  private String licenseFilePath;

  @Getter
  @Value("${broker.host}")
  private String brokerHost;

  @Getter
  @Value("${broker.mqtt.port}")
  private Integer brokerMqttPort;

  @Getter
  @Value("${broker.ws.port}")
  private Integer brokerWsPort;

  @Getter
  @Value("${mqtt.persistence.dir}")
  private String mqttPersistenceDir;

  @Getter
  @Value("${stbResourceXml.path}")
  private String stbResourceXmlPath;

  @Getter
  @Value("${backup.data.log.path.for.user.delete}")
  private String backupDataLogPathForUserDelete;

  @Getter
  @Value("${backup.data.log.file.name.for.user.delete}")
  private String backupDataLogFileNameForUserDelete;

  /****************
   * runtime properties
   ***********/
  private final PropertiesConfiguration runtimeProperties;

  @Value("${log.errorPrintStackTrace.enabled}")
  private Integer logErrorPrintStackTraceEnable;

  @Value("${log.businessException.enabled}")
  private Integer logBusinessExceptionEnabled;

  @Value("${account.resetPassword.possibleMinutes}")
  private Integer accountResetPasswordPossibleMinutes;

  @Value("${reverseProxy.enabled}")
  private Integer reverseProxyEnabled;

  @Value("${db.masterSlaveReplication.enabled}")
  private Integer dbMasterSlaveReplicationEnabled;

  @Value("${geoip.enabled}")
  private Integer geoipEnabled;

  @Value("${watchChannelCountScheduler.createWatchChannelCount.enabled}")
  private Integer watchChannelCountSchedulerCreateWatchChannelCountEnabled;

  @Value("${channelRankingScheduler.updateChannelRanking.termDay}")
  private Integer channelRankingSchedulerUpdateChannelRankingTermDay;

  @Value("${watchChannelStatsScheduler.saveStats.enabled}")
  private Integer watchChannelStatsSchedulerSaveStatsEnabled;

  @Value("${watchVodStatsScheduler.saveStats.enabled}")
  private Integer watchVodStatsSchedulerSaveStatsEnabled;

  @Value("${watchCatchupStatsScheduler.saveStats.enabled}")
  private Integer watchCatchupStatsSchedulerSaveStatsEnabled;

  @Value("${userLogin.saveStats.enabled}")
  private Integer userLoginSaveStatsEnabled;

  @Value("${imageResize.enabled}")
  private Integer imageResizeEnabled;

  @Value("${createScheduleTestData.enabled}")
  private Integer createScheduleTestDataEnabled;

  @Value("${libraryAvaiableSizeCheck.disabled}")
  private Integer libraryAvaiableSizeCheckDisabled;

  @Value("${scheduleDeleteScheduler.termDay}")
  private Integer scheduleDeleteSchedulerTermDay;

  @Value("${expiredInviteDelete.enabled}")
  private Integer expiredInviteDeleteEnabled;

  @Value("${watchChannelStatsScheduler.executeBeforeHour}")
  private Integer watchChannelStatsSchedulerExecuteBeforeHour;


  @Value("${subscribe.unitDays}")
  private Integer subscribeUnitDays;

  @Value("${watchChannelCountScheduler.close.beforeHours}")
  private Integer watchChannelCountSchedulerCloseBeforeHours;

  @Value("${gmmanager.aws.enabled}")
  private Integer gmmanagerAwsEnabled;

  @Value("${userDevice.slot.maxCount}")
  private Integer userDeviceSlotMaxCount;

  @Value("${account.emailVerification.enabled}")
  private Integer accountEmailVerificationEnabled;

  @Value("${notificationBatchScheduler.enabled}")
  private Integer notificationBatchSchedulerEnabled;

  @Value("${serviceCheck.diskUsage.enabled}")
  private Integer serviceCheckDiskUsageEnabled;

  @Value("${serviceCheck.diskUsage.thresholdPercentage}")
  private Integer serviceCheckDiskUsageThresholdPercentage;

  @Value("${serviceCheck.diskUsage.path}")
  private String serviceCheckDiskUsagePath;

  @Value("${serviceCheck.db.lock.enabled}")
  private Integer serviceCheckDblockEnabled;

  @Value("${serviceCheck.db.lock.sql}")
  private String serviceCheckDblockSql;

  @Value("${serviceCheck.db.queryExecutionTime.enabled}")
  private Integer serviceCheckDbQueryExecutionTimeEnabled;

  @Value("${serviceCheck.db.queryExecutionTime.sql}")
  private String serviceCheckDbQueryExecutionTimeSql;

  @Value("${serviceCheck.db.queryExecutionTime.thresholdMillis}")
  private Integer serviceCheckDbQueryExecutionTimeThresholdMillis;

  @Value("${serviceCheck.db.connectionCount.enabled}")
  private Integer serviceCheckDbConnectionCountEnabled;

  @Value("${serviceCheck.db.connectionCount.sql}")
  private String serviceCheckDbConnectionCountSql;

  @Value("${serviceCheck.db.connectionCount.threshold}")
  private Integer serviceCheckDbConnectionCountThreshold;

  @Value("${serviceCheck.db.replicationStatus.enabled}")
  private Integer serviceCheckDbReplicationStatusEnabled;

  @Value("${serviceCheck.db.replicationStatus.sql}")
  private String serviceCheckDbReplicationStatusSql;

  @Value("${serviceCheck.activeMQ.enabled}")
  private Integer serviceCheckActiveMQEnabled;

  @Value("${preventConcurrencyLogin.enabled}")
  private Integer preventConcurrencyLoginEnabled;

  @Value("${promotionWinnerBatchScheduler.enabled}")
  private Integer promotionWinnerBatchSchedulerEnabled;

  @Value("${serviceCheckBatchScheduler.enabled}")
  private Integer serviceCheckBatchSchedulerEnabled;

  @Value("${billingRequest.url}")
  private String billingRequestUrl;

  @Value("${gmmanager.encodeRequestWithSingleThread.enabled}")
  private Integer gmmanagerEncodeRequestWithSingleThreadEnabled;

  @Value("${accessCountryStatBatchScheduler.enabled}")
  private Integer accessCountryStatBatchSchedulerEnabled;

  @Value("${vodBadgeAssignBatchScheduler.enabled}")
  private Integer vodBadgeAssignBatchSchedulerEnabled;

  @Value("${player.drm.licenseServerAddress}")
  private String playerDrmLicenseServerAddress;

  @Value("${player.drm.userId}")
  private String playerDrmUserId;

  @Value("${player.drm.sessionId}")
  private String playerDrmSessionId;

  @Value("${player.drm.merchant}")
  private String playerDrmMerchant;

  @Value("${poc.catchup.schedule.url}")
  @Getter
  private String pocCatchupScheduleUrl;


  @Value("${watch.channel.log.csv.export.file.dir}")
  @Getter
  private String watchChannelLogFileDir;

  @Value("${mini-cdn.enabled}")
  @Getter
  private Integer miniCDNEnabled;


  @Value("${message.box.maximum.message}")
  @Getter
  private Integer maximumMessage;

  public ServicePropertiesUtil(PropertiesConfiguration runtimeProperties) {
    this.runtimeProperties = runtimeProperties;
  }

  @PostConstruct
  void init() {
    log.debug("init");
    this.channelVodDrmUrl = String.format("%s%s", this.gmmanagerAddress, drmUriChannelVod, "/gmanager/drmUrl/getUrl?channelId={0}");
    this.libraryUrl = String.format("%s%s", this.gmmanagerAddress, signedUriLibrary);
    this.channelLiveUrl = String.format("%s%s", this.gmmanagerAddress, signedUrlChannel);
    this.libraryDrmUrl = String.format("%s%s", this.gmmanagerAddress, drmUriLibrary);
    this.gmmanagerGetEncoderUrl = String.format("%s%s", gmmanagerAddress, gmmanagerGetEncoderUri);
    this.gmmanagerCancelEncodeUrl = String.format("%s%s", gmmanagerAddress, gmmanagerCancelEncodeUri);
    this.authDrmUrl = String.format("%s%s", this.gmmanagerAddress, drmUriAuth);
    this.uploadUrl = String.format("%s%s", this.storageHost, this.uploadUri);
    this.gmmanagerChannelEnableUrl = String.format("%s%s", gmmanagerAddress, gmmanagerChannelEnableUri);
    this.gmmanagerChannelDisableUrl = String.format("%s%s", gmmanagerAddress, gmmanagerChannelDisableUri);
    this.gmmanagerChannelDeleteUrl = String.format("%s%s", gmmanagerAddress, gmmanagerChannelDeleteUri);
    this.geoipPath = String.format("%s%s", this.geoipStoragePath, this.geoipUri);
    this.gmmanagerGetInstanceUsageUrl = String.format("%s%s", gmmanagerAddress, gmmanagerGetInstanceUsageUri);
    this.gmmanagerAwsStartUrl = String.format("%s%s", gmmanagerAddress, gmmanagerAwsStartUri);
    this.gmmanagerAwsStopUrl = String.format("%s%s", gmmanagerAddress, gmmanagerAwsStopUri);
    this.gmmanagerAwsTerminateUrl = String.format("%s%s", gmmanagerAddress, gmmanagerAwsTerminateUri);
    this.gmmanagerAwsCreateUrl = String.format("%s%s", gmmanagerAddress, gmmanagerAwsCreateUri);
    this.gmmanagerAwsCheckStatusUrl = String.format("%s%s", gmmanagerAddress, gmmanagerAwsCheckStatusUri);
    this.gmmanagerSetTodayScheduleUrl = String.format("%s%s", gmmanagerAddress, gmmanagerSetTodayScheduleUri);
    this.gmmanagerStartNowUrl = String.format("%s%s", gmmanagerAddress, gmmanagerStartNowUri);
    this.gmmanagerDistributeImageToLocalCdnUrl = String.format("%s%s", gmmanagerAddress, gmmanagerDistributeImageToLocalCdnUri);
    this.gmmanagerFairPlayPackagingRequestUrl = String.format("%s%s", gmmanagerAddress, this.gmmanagerFairPlayPackagingRequestUri);
    this.gmmanagerSoundTrackLanguageChangeRequestUrl = String.format("%s%s", gmmanagerAddress, this.gmmanagerSoundTrackLanguageChangeRequestUri);
    this.gmmanagerThumbnailUrl = String.format("%s%s", gmmanagerAddress, this.gmmanagerThumbnailUri);
    this.gmmanagerScheduleThumbnailUrl = String.format("%s%s", gmmanagerAddress, this.gmmanagerScheduleThumbnailUri);
  }

  public boolean isErrorPrintStackTraceEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("log.errorPrintStackTrace.enabled", this.logErrorPrintStackTraceEnable));
  }

  public boolean isLogBusinessExceptionEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("log.businessException.enabled", this.logBusinessExceptionEnabled));
  }

  public int getAccountResetPasswordPossibleMinutes() {
    return runtimeProperties.getInteger("account.resetPassword.possibleMinutes", this.accountResetPasswordPossibleMinutes);
  }

  public boolean isReverseProxyEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("reverseProxy.enabled", this.reverseProxyEnabled));
  }

  public boolean isDbMasterSlaveReplicationEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("db.masterSlaveReplication.enabled", this.dbMasterSlaveReplicationEnabled));
  }

  public boolean isGeoipEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("geoip.enabled", this.geoipEnabled));
  }

  public boolean isWatchChannelCountSchedulerCreateWatchChannelCountEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("watchChannelCountScheduler.createWatchChannelCount.enabled",
            this.watchChannelCountSchedulerCreateWatchChannelCountEnabled));
  }

  public int getChannelRankingSchedulerUpdateChannelRankingTermDay() {
    return runtimeProperties.getInteger("channelRankingScheduler.updateChannelRanking.termDay",
            this.channelRankingSchedulerUpdateChannelRankingTermDay);
  }

  public boolean isWatchChannelStatsSchedulerSaveStatsEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("watchChannelStatsScheduler.saveStats.enabled",
            this.watchChannelStatsSchedulerSaveStatsEnabled));
  }

  public boolean isVodStatsSchedulerSaveStatsEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("watchVodStatsScheduler.saveStats.enabled",
            this.watchVodStatsSchedulerSaveStatsEnabled));
  }

  public boolean isImageResizeEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("imageResize.enabled", this.imageResizeEnabled));
  }

  public boolean isCreateScheduleTestDataEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("createScheduleTestData.enabled", this.createScheduleTestDataEnabled));
  }

  public boolean isLibraryAvailableSizeCheckDisabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("libraryAvaiableSizeCheck.disabled", this.libraryAvaiableSizeCheckDisabled));
  }

  public int getScheduleDeleteSchedulerTermDay() {
    return runtimeProperties.getInteger("scheduleDeleteScheduler.termDay", this.scheduleDeleteSchedulerTermDay);
  }

  public boolean isAdvertiseAccessLogDeleteBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("advertiseAccessLogDeleteBatch.enabled", 1));
  }

  public int getWatchChannelStatsSchedulerBeforeHour() {
    return runtimeProperties.getInteger("watchChannelStatsScheduler.executeBeforeHour", this.watchChannelStatsSchedulerExecuteBeforeHour);
  }

  public boolean isGmmanagerAwsEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("gmmanager.aws.enabled", this.gmmanagerAwsEnabled));
  }

  public int getSubscribeUnitDays() {
    return runtimeProperties.getInteger("subscribe.unitDays", this.subscribeUnitDays);
  }

  public int getCloseWatchingStatusLogBeforeHours() {
    return runtimeProperties.getInteger("watchChannelCountScheduler.close.beforeHours", this.watchChannelCountSchedulerCloseBeforeHours);
  }

  public int getUserDeviceSlotMaxCount() {
    return runtimeProperties.getInteger("userDevice.slot.maxCount", this.userDeviceSlotMaxCount);
  }

  public int getUnlimitedUserDeviceSlotMaxCount() {
    return runtimeProperties.getInteger("unlimitedUserDevice.slot.maxCount", 10000);
  }

  public String getUploadPath() {
    return String.format("%s%s", this.storagePath, this.uploadUri);
  }

  public boolean isAccountEmailVerificatioinEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("account.emailVerification.enabled", this.accountEmailVerificationEnabled));
  }

  public boolean isNotificationBatchSchedulerEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("notificationBatchScheduler.enabled", this.notificationBatchSchedulerEnabled));
  }

  public boolean isServiceCheckDiskUsageEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("serviceCheck.diskUsage.enabled", this.serviceCheckDiskUsageEnabled));
  }

  public int getServiceCheckDiskUsageThresholdPercentage() {
    return runtimeProperties.getInteger("serviceCheck.diskUsage.thresholdPercentage", this.serviceCheckDiskUsageThresholdPercentage);
  }

  public String[] getServiceChecDiskUsagePath() {
    return getDefaultValues(runtimeProperties.getStringArray("serviceCheck.diskUsage.path"), StringUtils.split(this.serviceCheckDiskUsagePath, ","));
  }

  private String[] getDefaultValues(String[] values, String[] defaultValues) {
    return ArrayUtils.isEmpty(values) ? defaultValues : values;
  }

  public boolean isServiceCheckDbLockEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("serviceCheck.dblock.enabled", this.serviceCheckDblockEnabled));
  }

  public String getServiceCheckDbLockSql() {
    return runtimeProperties.getString("serviceCheck.db.lock.sql", this.serviceCheckDblockSql);
  }

  public Long getServiceCheckDbQueryExcecutionTimeThresholdMillis() {
    return runtimeProperties.getLong("serviceCheck.db.queryExecutionTime.thresholdMillis", this.serviceCheckDbQueryExecutionTimeThresholdMillis);
  }

  public String getServiceCheckDbQueryExecutionTimeSql() {
    return runtimeProperties.getString("serviceCheck.db.queryExecutionTime.sql", this.serviceCheckDbQueryExecutionTimeSql);
  }

  public boolean isServiceCheckDbQueryExecutionTimeEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("serviceCheck.db.queryExecutionTime.enabled",
            this.serviceCheckDbQueryExecutionTimeEnabled));
  }

  public String getServiceCheckDbConnectionCountSql() {
    return runtimeProperties.getString("serviceCheck.db.connectionCount.sql", this.serviceCheckDbConnectionCountSql);
  }

  public boolean isServiceCheckDbConnectionCountEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("serviceCheck.db.connectionCount.enabled", this.serviceCheckDbConnectionCountEnabled));
  }

  public boolean isServiceCheckDbReplicationStatusEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("serviceCheck.db.replicationStatus.enabled",
            this.serviceCheckDbReplicationStatusEnabled));
  }

  public String getServiceCheckDbReplicationStatusSql() {
    return runtimeProperties.getString("serviceCheck.db.replicationStatus.sql", this.serviceCheckDbReplicationStatusSql);
  }

  public Integer getServiceCheckDbConnectionCountThreshold() {
    return runtimeProperties.getInteger("serviceCheck.db.connectionCount.threshold", this.serviceCheckDbConnectionCountThreshold);
  }

  public boolean isServiceCheckActiveMQEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("serviceCheck.activeMQ.enabled", this.serviceCheckActiveMQEnabled));
  }

  public boolean isPreventConcurrencyLogin() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("preventConcurrencyLogin.enabled", this.preventConcurrencyLoginEnabled));
  }

  public boolean isPromotionWinnerBatchSchedulerEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("promotionWinnerBatchScheduler.enabled", this.promotionWinnerBatchSchedulerEnabled));
  }

  public String getLookTVBillingRequestUrl() {
    return runtimeProperties.getString("billingRequest.url", this.billingRequestUrl);
  }

  public boolean isServiceCheckBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("serviceCheckBatchScheduler.enabled", this.serviceCheckBatchSchedulerEnabled));
  }

  public String[] getServiceCheckFailHandlerEmails() {
    return getDefaultValues(runtimeProperties.getStringArray("serviceCheck.handler.email"), new String[]{"<EMAIL>"});
  }

  public String getServiceCheckUrl() {
    return runtimeProperties.getString("serviceCheck.url", "http://localhost:8080/api/service_check");
  }

  public boolean isUseNativeQueryForContinuePlay() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("nativeQuery.continuePlay.enabled", 1));
  }

  public boolean isUseNativeQueryForContinuePlayList() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("nativeQuery.continuePlayList.enabled", 1));
  }

  public long getPayTwicePossibleLeftDays() {
    return runtimeProperties.getLong("payTwice.possibleLeftDays", 7L);
  }

  public boolean isUseSingleThreadInEncodeBackgroundProcess() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("gmmanager.encodeRequestWithSingleThread.enabled",
            this.gmmanagerEncodeRequestWithSingleThreadEnabled));
  }

  public boolean isUseLibrarySourceBucket() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("useLibrarySourceBucket.enabled", 0));
  }

  public boolean isAccessCountryStatBatchSchedulerEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties
            .getInteger("accessCountryStatBatchScheduler.enabled", this.accessCountryStatBatchSchedulerEnabled));
  }

  public String getDrmPlayerLicenseServerAddress() {
    return runtimeProperties.getString("player.drm.licenseServerAddress", this.playerDrmLicenseServerAddress);
  }

  public String getDrmPlayerUserId() {
    return runtimeProperties.getString("player.drm.userId", this.playerDrmUserId);
  }

  public String getDrmPlayerSessionId() {
    return runtimeProperties.getString("player.drm.sessionId", this.playerDrmSessionId);
  }

  public String getDrmPlayerMerchant() {
    return runtimeProperties.getString("player.drm.merchant", this.playerDrmMerchant);
  }

  public boolean isVodBadgeAssignBatchSchedulerEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("vodBadgeAssignBatchScheduler.enabled", this.vodBadgeAssignBatchSchedulerEnabled));
  }

  public boolean isWatchVodStatsSchedulerSaveStatsEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("watchVodStatsScheduler.saveStats.enabled",
            this.watchVodStatsSchedulerSaveStatsEnabled));
  }

  public boolean isTranscodeChannelStatusEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("transcoderManager.channelStatus.enabled", 1));
  }

  public boolean isWatchCatchupStatsCollectEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("watchCatchupStatsScheduler.saveStats.enabled",
            this.watchCatchupStatsSchedulerSaveStatsEnabled));
  }

  public boolean isUseGmanagerForVodUrl() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("vod.signedUrlByGmanager.enabled", 0));
  }

  public boolean isUseFacebookLogin() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("account.facebookLogin.enabled", 0));
  }

  public String getRemoteControlKeyArray() {
    return runtimeProperties.getString("doku.remoteControlKey", "38,40,37,39,13"); // 상,하,좌,우,엔터
  }

  public String getDokuPaymentChannel() {
    return runtimeProperties.getString("doku.paymentChannel"); //15:일반페이, 16:Tokenization
  }

  public boolean isPopularVodSelectBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("vodSelect.popular.enabled", 1));
  }

  public boolean isNewVodSelectBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("vodSelect.new.enabled", 1));
  }

  public Long getWatchChannelLogSaveMaxDurationSeconds() {
    return runtimeProperties.getLong("watchLog.channel.maxDurationSeconds", 60L);
  }

  public int getAccessTokenExpireDays() {
    return runtimeProperties.getInt("oauth.accessToken.expireDays", 1);
  }

  public int getRefreshTokenExpireDays() {
    return runtimeProperties.getInt("oauth.refreshToken.expireDays", 30);
  }

  public boolean isUserLoginStatBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("userLogin.saveStats.enabled", this.userLoginSaveStatsEnabled));
  }

  public boolean isAdvertiseStatBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("advertise.saveStats.enabled", 1));
  }

  public Integer getAdvertiseAccessLogDeleteJobSize() {
//    return runtimeProperties.getInteger("batch.advertiseAccessLogDelete.JobSize", 10000);
    return runtimeProperties.getInteger("batch.advertiseAccessLogDelete.JobSize", 1500000);
  }

  public Integer getAdvertiseAcessLogDeleteStepSize() {
    return runtimeProperties.getInteger("batch.advertiseAccessLogDelete.StepSize", 200);
  }

  public boolean isUseAmqpForAdvertiseExpose() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("amqp.ad.enabled", 0));
  }

  public boolean isUseAccessLogByReadUnCommitted() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("accessLog.useReadUnCommitted.enabled", 0));
  }

  public boolean isUseMidtransCheckTransactionStatus() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("billing.midtrans.checkTransactionStatus.enabled", 0));
  }

  public String getMonetaryCumulativeStartDtUtc() {
    return runtimeProperties.getString("service.launchDt", "20200229170000");
  }

  public boolean isUseGlobalIpRestrctionForOttLogin() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("ottLogin.ipRestriction.enabled", 1));
  }

  public int getAccessCountryLogDeleteBeforeMonth() {
    return runtimeProperties.getInteger("batch.accessCountryLogDelete.beforeMonth", 1);
  }

  public int getAccessCountryLogDeleteJobSize() {
    return runtimeProperties.getInteger("batch.accessCountryLogDelete.jobSize", 20000);
  }

  public int getAccessCountryLogDeletStepSize() {
    return runtimeProperties.getInteger("batch.accessCountryLogDelete.stepSize", 300);
  }

  public boolean isAccessCountryLogDeleteBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("batch.accessCountryLogDelete.enabled", 1));
  }

  public int getUserLoginLogDeleteBeforeMonth() {
    return runtimeProperties.getInteger("batch.userLoginLogDelete.beforeMonth", 3);
  }

  public int getWatchChannelLogDeleteJobSize() {
    return runtimeProperties.getInteger("batch.watchChannelLogDelete.jobSize", 20000);
  }

  public boolean isWatchChannelLogDeleteBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("batch.watchChannelLogDelete.enabled", 1));
  }

  public boolean isWatchVodLogDeleteBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("batch.watchVodLogDelete.enabled", 1));
  }

  public boolean isUserLoginLogDeleteBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("batch.userLoginLogDelete.enabled", 1));
  }

  public int getUserLoginLogDeleteAddHours() {
    return runtimeProperties.getInteger("batch.userLoginLogDelete.addHours", 24);
  }
  public int getWatchChannelLogDeleteBeforeMonth() {
    return runtimeProperties.getInteger("batch.watchChannelLogDelete.beforeMonth", 7);
  }

  public int getWatchVodLogDeleteBeforeMonth() {
    return runtimeProperties.getInteger("batch.watchVodLogDelete.beforeMonth", 7);
  }

  public int getWatchVodLogDeleteJobSize() {
    return runtimeProperties.getInteger("batch.watchVodLogDelete.jobSize", 20000);
  }

  public int getWatchChannelLogDeleteStepSize() {
    return runtimeProperties.getInteger("batch.watchChannelLogDelete.stepSize", 300);
  }

  public int getWatchVodLogDeletStepSize() {
    return runtimeProperties.getInteger("batch.watchVodLogDelete.stepSize", 300);
  }

  public boolean isAccessCountryLogSaveEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("logging.accessCountryLog.enabled", 0));
  }

  public boolean isUseCacheForSchedule() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("cache.schedule.enabled", 0));
  }

  public boolean isUseCacheForChannels() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("cache.channel.enabled", 1));
  }

  public boolean isUseCacheForChannelsByPackage() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("cache.channels.by.package.enabled", 1));
  }

  public int getScheduleFileFromHour() {
    return runtimeProperties.getInteger("sdf.scheduleFile.fromHour", 0);
  }

  public int getScheduleFileCatchupFromHour() {
    return runtimeProperties.getInteger("sdf.scheduleFile.catchup.fromHour", -168);
  }

  public int getScheduleFileToHour() {
    return runtimeProperties.getInteger("sdf.scheduleFile.toHour", 168);
  }

  public String getScheduleFileTempPath(String channelId) {
    return runtimeProperties.getString("sdf.scheduleFile.tempPath", String.format("%s/tmp/%s/%d", getStoragePath(),
            channelId, DateUtil.getDate().getTime()));
  }

  public String getScheduleFilePath(String channelId, long timestamp) {
    return runtimeProperties.getString("sdf.scheduleFile.path", String.format("%s/epg/%s", getStoragePath(),
            channelId));
  }

  public boolean isUserScheduleFileCreateBatchEnabled() {
    return BooleanUtils.toBoolean(runtimeProperties.getInteger("batch.scheduleFileCreate.enabled", 1));
  }

  public int getRsCalcStandardDayOfMonth() {
    return runtimeProperties.getInteger("sms.rsCalc.dayOfMonth", 20);
  }


  // added by rakie
  // 언제까지 RawData를 유지할지. (2달 전꺼까지)
  public int getDeleteKeepDayCount() {
    return runtimeProperties.getInteger("del.campaignAccess.keepDays", 60);
  }

  // 한번 작업할때 7일치를 지운다.
  public int getDeleteJobDayCount() {
    return runtimeProperties.getInteger("del.campaignAccess.jobDays", 7);
  }

  // 한번 지울때 몇개씩 지울까?   1000개씩 지운다.
  public int getDeleteCountOnce() {
    return runtimeProperties.getInteger("del.campaignAccess.deleteOnce", 1000);
  }

  // 최대 SQL 작업제한
  public int getDeleteMaxSqlTryCount() {
    return runtimeProperties.getInteger("del.campaignAccess.maxSqlTry", 3000);
  }
}
