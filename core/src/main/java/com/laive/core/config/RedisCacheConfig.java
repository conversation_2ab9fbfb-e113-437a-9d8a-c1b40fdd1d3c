package com.laive.core.config;

import com.laive.commons.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.interceptor.SimpleCacheErrorHandler;
import org.springframework.cache.interceptor.SimpleCacheResolver;
import org.springframework.cache.interceptor.SimpleKeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
@EnableCaching
@ConditionalOnProperty(prefix = "cache", name = "storage", havingValue = "REDIS")
public class RedisCacheConfig implements CachingConfigurer {

  public static final String CACHE_CODES = "commonCodes";
  public static final String CACHE_SUPPORTED_LANGUAGES = "supportedLanguages";
  public static final String CACHE_PLATFORM = "platforms";
  public static final String CACHE_LICENSE_TYPE = "licentTypes";
  public static final String CACHE_GENRE = "genres";
  public static final String CACHE_STUDIO = "studios";

  public static final String CACHE_ROOT_CATEGORY = "rootCategory";
  public static final String CACHE_CONFIG = "config";
  public static final String CACHE_CHILD_CATEGORIES = "childCategories";
  public static final String CACHE_FREE_USER = "freeUser";
  public static final String CACHE_IS_WATCHABLE = "isWatchable";
  public static final String CACHE_WATCH_USER_TIMESTAMP = "CACHE_WATCH_USER_TIMESTAMP";
  public static final String CACHE_SCHEDULES = "CACHE_SCHEDULES";
  public static final String CACHE_CHANNELS = "CACHE_CHANNELS";
  public static final String CACHE_POPULAR_CHANNELS = "CACHE_POPULAR_CHANNELS";
  public static final String CACHE_CHANNELS_BY_PACKAGE = "CACHE_CHANNELS_BY_PACKAGE";
  public static final String CACHE_CHANNELS_ALL = "CACHE_CHANNELS_ALL";

  @Autowired
  private Environment env;

  @Bean
  public RedisTemplate<String, String> redisTemplate() {
    RedisTemplate<String, String> redisTemplate = new RedisTemplate<>();
    redisTemplate.setConnectionFactory(redisConnectionFactory());
    return redisTemplate;
  }

  @Bean
  public RedisTemplate<String, Object> redisObjectTemplate() {
    RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
    redisTemplate.setConnectionFactory(redisConnectionFactory());
    return redisTemplate;
  }

  @Bean
  public JedisConnectionFactory redisConnectionFactory() {
    String host = env.getProperty("redis.host");
    Integer port = Integer.parseInt(env.getProperty("redis.port"));
    String password = env.getProperty("redis.password");

    JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
    jedisConnectionFactory.setHostName(host);
    jedisConnectionFactory.setPort(port);
    jedisConnectionFactory.setUsePool(true);
    jedisConnectionFactory.setDatabase(4);

    if (StringUtils.isNotBlank(password))
      jedisConnectionFactory.setPassword(password);
    return jedisConnectionFactory;
  }

  @Bean
  public CacheManager cacheManager() {
    RedisCacheManager cacheManager = new RedisCacheManager(redisTemplate());
    cacheManager.setDefaultExpiration(300L); // 0 : unlimited, unit:seconds
    cacheManager.setUsePrefix(true); // if true, cacheName:key 로 저장된다. false 이면 key 만으로 저장됨으로 같은 키인경우 문제가 될수 있다.

    List<String> cacheNames = new ArrayList<>();
    cacheNames.add(CACHE_CODES);
    cacheNames.add(CACHE_SUPPORTED_LANGUAGES);
    cacheNames.add(CACHE_PLATFORM);
    cacheNames.add(CACHE_LICENSE_TYPE);
    cacheNames.add(CACHE_GENRE);
    cacheNames.add(CACHE_ROOT_CATEGORY);
    cacheNames.add(CACHE_CONFIG);
    cacheNames.add(CACHE_CHILD_CATEGORIES);
    cacheNames.add(CACHE_FREE_USER);
    cacheNames.add(CACHE_IS_WATCHABLE);
    cacheNames.add(CACHE_STUDIO);
    cacheNames.add(CACHE_WATCH_USER_TIMESTAMP);
    cacheNames.add(CACHE_SCHEDULES);
    cacheNames.add(CACHE_CHANNELS);
    cacheNames.add(CACHE_POPULAR_CHANNELS);
    cacheNames.add(CACHE_CHANNELS_BY_PACKAGE);
    cacheNames.add(CACHE_CHANNELS_ALL);
    cacheNames.add("simple-test");
    cacheManager.setCacheNames(cacheNames);

    Map<String, Long> expiresMap = new HashMap<>();
    expiresMap.put(CACHE_CODES, 600L);
    expiresMap.put(CACHE_SUPPORTED_LANGUAGES, 600L);
    expiresMap.put(CACHE_PLATFORM, 600L);
    expiresMap.put(CACHE_LICENSE_TYPE, 600L);
    expiresMap.put(CACHE_GENRE, 600L);
    expiresMap.put(CACHE_ROOT_CATEGORY, 600L);
    expiresMap.put(CACHE_CONFIG, (long)(60 * 10));
    expiresMap.put(CACHE_CHILD_CATEGORIES, (long)(60 * 10));
    expiresMap.put(CACHE_FREE_USER, (long)(60 * 10));
    expiresMap.put(CACHE_IS_WATCHABLE, 60L);
    expiresMap.put(CACHE_STUDIO, 600L);
    expiresMap.put(CACHE_WATCH_USER_TIMESTAMP, (long)(60 * 60 * 24));
    expiresMap.put(CACHE_SCHEDULES, (long)(60 * 10));
    expiresMap.put(CACHE_CHANNELS, (long)(60 * 10));
    expiresMap.put(CACHE_CHANNELS_BY_PACKAGE, (long)(60 * 10)); // 10 minutes
    expiresMap.put(CACHE_CHANNELS_ALL, (long)(60 * 15)); // 15 minutes for all channels cache
    expiresMap.put("simple-test", 300L); // 5 minutes for simple test
    cacheManager.setExpires(expiresMap);
    return cacheManager;
  }

  @Override
  public CacheResolver cacheResolver() {
    return new SimpleCacheResolver(cacheManager());
  }

  @Override
  public KeyGenerator keyGenerator() {
    return new SimpleKeyGenerator();
  }

  @Override
  public CacheErrorHandler errorHandler() {
    return new SimpleCacheErrorHandler();
  }
}
